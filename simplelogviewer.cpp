#include "simplelogviewer.h"
#include "simplefiledatasource.h"
#include "simplelog4qtdatasource.h"
#include "simpleconfigmanager.h"

#include <QHeaderView>
#include <QMessageBox>
#include <QDebug>
#include <QTimer>
#include <QDialog>
#include <QSplitter>
#include <QTextEdit>
#include <QApplication>
#include <QClipboard>
#include <QFormLayout>
#include <QVBoxLayout>
#include <QHBoxLayout>

SimpleLogViewer::SimpleLogViewer(ViewerType type, QWidget* parent)
    : QWidget(parent)
    , m_type(type)
    , m_model(nullptr)
    , m_proxyModel(nullptr)
    , m_dataSource(nullptr)
    , m_mainLayout(nullptr)
    , m_mainSplitter(nullptr)
    , m_controlGroup(nullptr)
    , m_controlLayout(nullptr)
    , m_searchEdit(nullptr)
    , m_clearSearchButton(nullptr)
    , m_debugCheckBox(nullptr)
    , m_infoCheckBox(nullptr)
    , m_warningCheckBox(nullptr)
    , m_errorCheckBox(nullptr)
    , m_criticalCheckBox(nullptr)
    , m_tableView(nullptr)
    , m_statusLabel(nullptr)
    , m_progressBar(nullptr)
    , m_currentStatus("未连接")
{
    // 初始化日志级别过滤器（默认显示所有级别）
    m_currentLevelFilter.insert(LogEntry::LogLevel::Debug);
    m_currentLevelFilter.insert(LogEntry::LogLevel::Info);
    m_currentLevelFilter.insert(LogEntry::LogLevel::Warning);
    m_currentLevelFilter.insert(LogEntry::LogLevel::Error);
    m_currentLevelFilter.insert(LogEntry::LogLevel::Critical);
    
    setupUI();
    connectSignals();
    createDataSource();
    
    qDebug() << QString("SimpleLogViewer created with type: %1")
                .arg(type == FileViewer ? "FileViewer" : "Log4QtViewer");
}

SimpleLogViewer::~SimpleLogViewer()
{
    if (m_dataSource) {
        m_dataSource->disconnect();
    }
    qDebug() << "SimpleLogViewer destroyed";
}

void SimpleLogViewer::setupUI()
{
    // 创建主布局
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(5, 5, 5, 5);
    m_mainLayout->setSpacing(5);
    
    // 创建控制面板
    m_controlGroup = new QGroupBox("控制面板", this);
    m_controlLayout = new QHBoxLayout(m_controlGroup);
    
    // 搜索组件
    m_controlLayout->addWidget(new QLabel("搜索:"));
    m_searchEdit = new QLineEdit();
    m_searchEdit->setPlaceholderText("输入搜索关键词...");
    m_controlLayout->addWidget(m_searchEdit);
    
    m_clearSearchButton = new QPushButton("清除");
    m_clearSearchButton->setMaximumWidth(60);
    m_controlLayout->addWidget(m_clearSearchButton);
    
    m_controlLayout->addWidget(new QLabel("|"));
    
    // 日志级别过滤组件
    m_controlLayout->addWidget(new QLabel("级别:"));
    m_debugCheckBox = new QCheckBox("Debug");
    m_debugCheckBox->setChecked(true);
    m_controlLayout->addWidget(m_debugCheckBox);
    
    m_infoCheckBox = new QCheckBox("Info");
    m_infoCheckBox->setChecked(true);
    m_controlLayout->addWidget(m_infoCheckBox);
    
    m_warningCheckBox = new QCheckBox("Warning");
    m_warningCheckBox->setChecked(true);
    m_controlLayout->addWidget(m_warningCheckBox);
    
    m_errorCheckBox = new QCheckBox("Error");
    m_errorCheckBox->setChecked(true);
    m_controlLayout->addWidget(m_errorCheckBox);
    
    m_criticalCheckBox = new QCheckBox("Critical");
    m_criticalCheckBox->setChecked(true);
    m_controlLayout->addWidget(m_criticalCheckBox);

    m_controlLayout->addWidget(new QLabel("|"));

    // 自动滚动控制
    QCheckBox* autoScrollCheckBox = new QCheckBox("自动滚动");
    autoScrollCheckBox->setChecked(true);
    m_controlLayout->addWidget(autoScrollCheckBox);

    // 连接自动滚动信号
    connect(autoScrollCheckBox, &QCheckBox::toggled, [this](bool enabled) {
        SimpleConfigManager& config = SimpleConfigManager::instance();
        config.setAutoScroll(enabled);
        qDebug() << "Auto scroll toggled:" << enabled;
    });

    m_controlLayout->addWidget(new QLabel("|"));

    // 清除所有过滤器按钮
    QPushButton* clearAllButton = new QPushButton("清除所有");
    clearAllButton->setMaximumWidth(80);
    m_controlLayout->addWidget(clearAllButton);

    // 连接清除所有过滤器信号
    connect(clearAllButton, &QPushButton::clicked, [this]() {
        // 清除搜索
        m_searchEdit->clear();

        // 重置所有级别过滤器
        m_debugCheckBox->setChecked(true);
        m_infoCheckBox->setChecked(true);
        m_warningCheckBox->setChecked(true);
        m_errorCheckBox->setChecked(true);
        m_criticalCheckBox->setChecked(true);

        qDebug() << "All filters cleared";
    });

    m_controlLayout->addStretch();
    
    // 创建数据模型
    m_model = new LogModel(this);
    m_proxyModel = new LogSortFilterProxyModel(this);
    m_proxyModel->setSourceModel(m_model);
    
    // 创建表格视图（使用标准QTableView，不使用虚拟化）
    m_tableView = new QTableView(this);
    m_tableView->setModel(m_proxyModel);
    m_tableView->setAlternatingRowColors(true);
    m_tableView->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_tableView->setSortingEnabled(true);
    m_tableView->verticalHeader()->setVisible(false);
    
    // 设置表格列宽
    QHeaderView* header = m_tableView->horizontalHeader();
    header->setStretchLastSection(true);
    header->resizeSection(LogModel::TimestampColumn, 180);
    header->resizeSection(LogModel::LevelColumn, 80);
    header->resizeSection(LogModel::SourceColumn, 120);
    
    // 创建状态栏
    QHBoxLayout* statusLayout = new QHBoxLayout();
    m_statusLabel = new QLabel(m_currentStatus);
    m_progressBar = new QProgressBar();
    m_progressBar->setVisible(false);
    m_progressBar->setMaximumWidth(200);
    
    statusLayout->addWidget(m_statusLabel);
    statusLayout->addStretch();
    statusLayout->addWidget(m_progressBar);
    
    // 添加到主布局
    m_mainLayout->addWidget(m_controlGroup);
    m_mainLayout->addWidget(m_tableView, 1); // 表格占据主要空间
    m_mainLayout->addLayout(statusLayout);
    
    qDebug() << "SimpleLogViewer UI setup completed";
}

void SimpleLogViewer::connectSignals()
{
    // 搜索相关信号
    connect(m_searchEdit, &QLineEdit::textChanged,
            this, &SimpleLogViewer::onSearchTextChanged);
    connect(m_clearSearchButton, &QPushButton::clicked, [this]() {
        m_searchEdit->clear();
    });
    
    // 日志级别过滤信号
    connect(m_debugCheckBox, &QCheckBox::toggled,
            this, &SimpleLogViewer::onLevelFilterChanged);
    connect(m_infoCheckBox, &QCheckBox::toggled,
            this, &SimpleLogViewer::onLevelFilterChanged);
    connect(m_warningCheckBox, &QCheckBox::toggled,
            this, &SimpleLogViewer::onLevelFilterChanged);
    connect(m_errorCheckBox, &QCheckBox::toggled,
            this, &SimpleLogViewer::onLevelFilterChanged);
    connect(m_criticalCheckBox, &QCheckBox::toggled,
            this, &SimpleLogViewer::onLevelFilterChanged);

    // 表格双击事件
    connect(m_tableView, &QTableView::doubleClicked,
            this, &SimpleLogViewer::onTableDoubleClicked);

    qDebug() << "SimpleLogViewer signals connected";
}

void SimpleLogViewer::createDataSource()
{
    if (m_type == FileViewer) {
        m_dataSource = std::make_unique<SimpleFileDataSource>(this);
        qDebug() << "Created SimpleFileDataSource";
    } else {
        m_dataSource = std::make_unique<SimpleLog4QtDataSource>(this);
        qDebug() << "Created SimpleLog4QtDataSource";
    }
    
    // 连接数据源信号
    if (m_dataSource) {
        connect(m_dataSource.get(), &IDataSource::dataReady,
                this, &SimpleLogViewer::onDataReceived);
        connect(m_dataSource.get(), &IDataSource::error,
                this, &SimpleLogViewer::onDataSourceError);
        connect(m_dataSource.get(), &IDataSource::statusChanged,
                this, &SimpleLogViewer::onDataSourceStatusChanged);

        qDebug() << "SimpleLogViewer: 已连接dataReady信号到onDataReceived槽";
    }
}

bool SimpleLogViewer::loadFile(const QString& filePath, const QString& encoding)
{
    if (m_type != FileViewer) {
        showError("当前不是文件查看器模式");
        return false;
    }
    
    if (!m_dataSource) {
        showError("数据源未初始化");
        return false;
    }
    
    // 清除现有数据
    clear();
    
    // 设置文件路径和编码（通过SimpleFileDataSource的特定方法）
    SimpleFileDataSource* fileDataSource = 
        static_cast<SimpleFileDataSource*>(m_dataSource.get());
    fileDataSource->setFilePath(filePath);
    fileDataSource->setEncoding(encoding);
    
    // 连接到数据源
    bool success = m_dataSource->connectToSource();
    if (success) {
        m_currentStatus = QString("正在加载文件: %1").arg(filePath);
        updateStatus();
        m_progressBar->setVisible(true);

        // 启动异步加载数据（数据将通过dataReady信号异步到达）
        QTimer::singleShot(0, [this]() {
            // loadData()启动异步读取，实际数据通过dataReady信号发送
            // 不再直接调用onDataReceived()，因为loadData()返回的是空向量
            m_dataSource->loadData();
            qDebug() << "异步文件加载已启动，等待dataReady信号...";
        });
    } else {
        showError("无法连接到文件数据源");
    }
    
    return success;
}

bool SimpleLogViewer::connectLog4Qt(const QString& loggerName)
{
    if (m_type != Log4QtViewer) {
        showError("当前不是Log4Qt查看器模式");
        return false;
    }
    
    if (!m_dataSource) {
        showError("数据源未初始化");
        return false;
    }
    
    // 设置日志器名称（通过SimpleLog4QtDataSource的特定方法）
    SimpleLog4QtDataSource* log4qtDataSource = 
        static_cast<SimpleLog4QtDataSource*>(m_dataSource.get());
    log4qtDataSource->setLoggerName(loggerName);
    
    // 连接到数据源
    bool success = m_dataSource->connectToSource();
    if (success) {
        m_currentStatus = QString("已连接到Log4Qt日志器: %1").arg(loggerName);
        updateStatus();
        emit statusChanged(m_currentStatus);
    } else {
        showError("无法连接到Log4Qt数据源");
    }
    
    return success;
}

void SimpleLogViewer::setTextFilter(const QString& filter)
{
    m_currentFilter = filter;
    m_searchEdit->setText(filter);

    // 应用过滤器到代理模型 - 使用自定义的setFilterPattern方法
    if (m_proxyModel) {
        m_proxyModel->setFilterPattern(filter);
    }

    qDebug() << "Text filter set to:" << filter;
}

void SimpleLogViewer::setLevelFilter(const QSet<LogEntry::LogLevel>& levels)
{
    m_currentLevelFilter = levels;
    
    // 更新复选框状态
    m_debugCheckBox->setChecked(levels.contains(LogEntry::LogLevel::Debug));
    m_infoCheckBox->setChecked(levels.contains(LogEntry::LogLevel::Info));
    m_warningCheckBox->setChecked(levels.contains(LogEntry::LogLevel::Warning));
    m_errorCheckBox->setChecked(levels.contains(LogEntry::LogLevel::Error));
    m_criticalCheckBox->setChecked(levels.contains(LogEntry::LogLevel::Critical));
    
    // 应用级别过滤器到代理模型
    if (m_proxyModel) {
        m_proxyModel->setLevelFilters(levels);
    }
    
    qDebug() << "Level filter updated, count:" << levels.size();
}

void SimpleLogViewer::clear()
{
    // 首先停止任何正在进行的异步操作，避免在清除期间产生数据竞争
    if (m_dataSource) {
        // 如果是文件数据源，取消异步读取操作
        if (m_type == FileViewer) {
            SimpleFileDataSource* fileDataSource =
                static_cast<SimpleFileDataSource*>(m_dataSource.get());
            fileDataSource->cancelAsyncLoading();
        }
    }

    // 然后清除模型数据
    if (m_model) {
        m_model->clear();
    }

    m_currentStatus = "已清除";
    updateStatus();

    emit dataLoaded(0);
    qDebug() << "SimpleLogViewer cleared";
}

bool SimpleLogViewer::isConnected() const
{
    return m_dataSource && m_dataSource->isConnected();
}

int SimpleLogViewer::getLogCount() const
{
    return m_model ? m_model->getTotalCount() : 0;
}

QString SimpleLogViewer::getStatusInfo() const
{
    return m_currentStatus;
}

// ========== 私有槽函数实现 ==========

void SimpleLogViewer::onDataReceived(const QVector<LogEntry>& entries)
{
    qDebug() << "SimpleLogViewer::onDataReceived 被调用，接收到" << entries.size() << "条数据";

    if (!m_model) {
        qWarning() << "Model is null, cannot add entries";
        showError("数据模型未初始化");
        return;
    }

    if (entries.isEmpty()) {
        qDebug() << "Received empty entries - 这可能是问题所在！";
        m_progressBar->setVisible(false);
        m_currentStatus = "没有接收到数据";
        updateStatus();
        return;
    }

    try {
        // 添加数据到模型
        m_model->addLogEntries(entries);

        // 更新状态
        int totalCount = m_model->getTotalCount();
        m_currentStatus = QString("数据加载完成");
        updateStatus();

        // 隐藏进度条
        m_progressBar->setVisible(false);

        // 自动滚动到底部（如果配置允许）
        SimpleConfigManager& config = SimpleConfigManager::instance();
        if (config.getAutoScroll()) {
            QTimer::singleShot(100, [this]() {
                m_tableView->scrollToBottom();
            });
        }

        // 发出数据加载完成信号
        emit dataLoaded(totalCount);

        qDebug() << QString("Data received: %1 entries, total: %2")
                    .arg(entries.size()).arg(totalCount);

    } catch (const std::exception& e) {
        QString error = QString("数据处理异常: %1").arg(e.what());
        showError(error);
        m_progressBar->setVisible(false);
    } catch (...) {
        showError("数据处理时发生未知异常");
        m_progressBar->setVisible(false);
    }
}

void SimpleLogViewer::onSearchTextChanged()
{
    QString filter = m_searchEdit->text();
    m_currentFilter = filter;

    // 应用文本过滤器到代理模型 - 使用自定义的setFilterPattern方法
    if (m_proxyModel) {
        m_proxyModel->setFilterPattern(filter);
    }

    qDebug() << "Search text changed to:" << filter;
}

void SimpleLogViewer::onLevelFilterChanged()
{
    // 更新当前级别过滤器
    m_currentLevelFilter.clear();

    if (m_debugCheckBox->isChecked()) {
        m_currentLevelFilter.insert(LogEntry::LogLevel::Debug);
    }
    if (m_infoCheckBox->isChecked()) {
        m_currentLevelFilter.insert(LogEntry::LogLevel::Info);
    }
    if (m_warningCheckBox->isChecked()) {
        m_currentLevelFilter.insert(LogEntry::LogLevel::Warning);
    }
    if (m_errorCheckBox->isChecked()) {
        m_currentLevelFilter.insert(LogEntry::LogLevel::Error);
    }
    if (m_criticalCheckBox->isChecked()) {
        m_currentLevelFilter.insert(LogEntry::LogLevel::Critical);
    }

    // 应用级别过滤器到代理模型
    if (m_proxyModel) {
        m_proxyModel->setLevelFilters(m_currentLevelFilter);
    }

    // 更新状态显示
    updateStatus();

    qDebug() << "Level filter changed, active levels:" << m_currentLevelFilter.size();
}

void SimpleLogViewer::onDataSourceError(const QString& error)
{
    showError(error);
    m_progressBar->setVisible(false);

    m_currentStatus = QString("错误: %1").arg(error);
    updateStatus();

    emit errorOccurred(error);
    qDebug() << "Data source error:" << error;
}

void SimpleLogViewer::onDataSourceStatusChanged(const QString& status)
{
    m_currentStatus = status;
    updateStatus();

    emit statusChanged(status);
    qDebug() << "Data source status changed:" << status;
}

// ========== 私有辅助方法实现 ==========

void SimpleLogViewer::updateStatus()
{
    if (m_statusLabel) {
        // 构建详细的状态信息
        QString detailedStatus = m_currentStatus;

        if (m_model) {
            int totalCount = m_model->getTotalCount();
            int visibleCount = m_proxyModel ? m_proxyModel->rowCount() : totalCount;

            if (totalCount > 0) {
                if (visibleCount != totalCount) {
                    detailedStatus += QString(" | 显示: %1/%2 条").arg(visibleCount).arg(totalCount);
                } else {
                    detailedStatus += QString(" | 总计: %1 条").arg(totalCount);
                }

                // 显示过滤器状态
                if (!m_currentFilter.isEmpty()) {
                    detailedStatus += QString(" | 搜索: \"%1\"").arg(m_currentFilter);
                }

                if (m_currentLevelFilter.size() < 5) {
                    detailedStatus += QString(" | 级别过滤: %1 项").arg(m_currentLevelFilter.size());
                }
            }
        }

        m_statusLabel->setText(detailedStatus);
    }
}

void SimpleLogViewer::showError(const QString& error)
{
    qWarning() << "SimpleLogViewer error:" << error;

    // 可以选择显示消息框或仅记录日志
    // QMessageBox::warning(this, "错误", error);

    // 更新状态显示
    m_currentStatus = QString("错误: %1").arg(error);
    updateStatus();
}

void SimpleLogViewer::onTableDoubleClicked(const QModelIndex& index)
{
    if (!index.isValid() || !m_model) {
        return;
    }

    // 获取源模型索引（如果使用了代理模型）
    QModelIndex sourceIndex = index;
    if (m_proxyModel) {
        sourceIndex = m_proxyModel->mapToSource(index);
    }

    // 获取日志条目
    if (sourceIndex.row() >= 0 && sourceIndex.row() < m_model->rowCount()) {
        LogEntry entry = m_model->getLogEntry(sourceIndex.row());
        showLogDetailsDialog(entry);
    }
}

void SimpleLogViewer::showLogDetailsDialog(const LogEntry& entry)
{
    // 防止重复打开对话框
    static bool isDialogOpen = false;
    if (isDialogOpen) {
        return;
    }

    isDialogOpen = true;

    // 创建详情对话框
    QDialog dialog(this);
    dialog.setWindowTitle("日志详细信息");
    dialog.setMinimumSize(800, 600);
    dialog.resize(900, 700);

    QVBoxLayout* mainLayout = new QVBoxLayout(&dialog);

    // 创建分割器用于更好的布局
    QSplitter* splitter = new QSplitter(Qt::Vertical, &dialog);

    // === 上部分：基本信息 ===
    QWidget* infoWidget = new QWidget();
    infoWidget->setStyleSheet("QWidget { background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 12px; }");
    QFormLayout* formLayout = new QFormLayout(infoWidget);

    // 时间戳
    QLabel* timestampLabel = new QLabel(entry.timestamp().toString("yyyy-MM-dd hh:mm:ss.zzz dddd"));
    timestampLabel->setStyleSheet("font-weight: bold; color: #2c3e50; font-family: 'Consolas', monospace;");
    formLayout->addRow("📅 时间戳:", timestampLabel);

    // 日志级别（带颜色区分）
    QLabel* levelLabel = new QLabel(entry.levelString());
    QString levelColor;
    QString levelIcon;
    switch (entry.level()) {
        case LogEntry::LogLevel::Debug:
            levelColor = "#6c757d"; levelIcon = "🔍"; break;
        case LogEntry::LogLevel::Info:
            levelColor = "#17a2b8"; levelIcon = "ℹ️"; break;
        case LogEntry::LogLevel::Warning:
            levelColor = "#ffc107"; levelIcon = "⚠️"; break;
        case LogEntry::LogLevel::Error:
            levelColor = "#dc3545"; levelIcon = "❌"; break;
        case LogEntry::LogLevel::Critical:
            levelColor = "#6f42c1"; levelIcon = "💥"; break;
    }
    levelLabel->setStyleSheet(QString("font-weight: bold; color: %1; font-size: 14px;").arg(levelColor));
    formLayout->addRow(QString("%1 级别:").arg(levelIcon), levelLabel);

    // 来源
    QLabel* sourceLabel = new QLabel(entry.source());
    sourceLabel->setStyleSheet("font-family: 'Consolas', monospace; background-color: #e9ecef; padding: 4px 8px; border-radius: 4px; border: 1px solid #ced4da;");
    formLayout->addRow("📍 来源:", sourceLabel);

    // 消息长度信息
    QLabel* lengthLabel = new QLabel(QString("%1 字符").arg(entry.message().length()));
    lengthLabel->setStyleSheet("color: #6c757d; font-size: 12px;");
    formLayout->addRow("📏 消息长度:", lengthLabel);

    splitter->addWidget(infoWidget);

    // === 中部分：消息内容 ===
    QWidget* messageWidget = new QWidget();
    QVBoxLayout* messageLayout = new QVBoxLayout(messageWidget);

    QLabel* messageLabel = new QLabel("💬 消息内容:");
    messageLabel->setStyleSheet("font-weight: bold; font-size: 14px; margin-bottom: 8px;");
    messageLayout->addWidget(messageLabel);

    QTextEdit* messageEdit = new QTextEdit();
    messageEdit->setPlainText(entry.message());
    messageEdit->setReadOnly(true);
    messageEdit->setFont(QFont("Consolas", 11));
    messageEdit->setStyleSheet("QTextEdit { border: 2px solid #e9ecef; border-radius: 6px; padding: 8px; background-color: #ffffff; }");
    messageEdit->setMinimumHeight(200);
    messageLayout->addWidget(messageEdit);

    splitter->addWidget(messageWidget);

    // === 下部分：详细信息 ===
    if (!entry.details().isEmpty()) {
        QWidget* detailsWidget = new QWidget();
        QVBoxLayout* detailsLayout = new QVBoxLayout(detailsWidget);

        QLabel* detailsLabel = new QLabel("🔍 详细信息:");
        detailsLabel->setStyleSheet("font-weight: bold; font-size: 14px; margin-bottom: 8px;");
        detailsLayout->addWidget(detailsLabel);

        QTextEdit* detailsEdit = new QTextEdit();
        detailsEdit->setPlainText(entry.details());
        detailsEdit->setReadOnly(true);
        detailsEdit->setFont(QFont("Consolas", 10));
        detailsEdit->setStyleSheet("QTextEdit { border: 2px solid #e9ecef; border-radius: 6px; padding: 8px; background-color: #f8f9fa; }");
        detailsEdit->setMinimumHeight(120);
        detailsLayout->addWidget(detailsEdit);

        splitter->addWidget(detailsWidget);
    }

    // 设置分割器比例
    splitter->setStretchFactor(0, 0); // 基本信息固定
    splitter->setStretchFactor(1, 3); // 消息内容主要空间
    if (!entry.details().isEmpty()) {
        splitter->setStretchFactor(2, 1); // 详细信息较小空间
    }

    mainLayout->addWidget(splitter);

    // === 按钮区域 ===
    QHBoxLayout* buttonLayout = new QHBoxLayout();

    // 复制全部按钮
    QPushButton* copyAllButton = new QPushButton("📋 复制全部");
    copyAllButton->setStyleSheet("QPushButton { background-color: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; font-weight: bold; } QPushButton:hover { background-color: #0056b3; }");
    connect(copyAllButton, &QPushButton::clicked, [&entry]() {
        QString fullText = QString("时间: %1\n级别: %2\n来源: %3\n\n消息:\n%4\n\n详情:\n%5")
            .arg(entry.timestamp().toString("yyyy-MM-dd hh:mm:ss.zzz dddd"))
            .arg(entry.levelString())
            .arg(entry.source())
            .arg(entry.message())
            .arg(entry.details());
        QApplication::clipboard()->setText(fullText);
    });
    buttonLayout->addWidget(copyAllButton);

    // 复制消息按钮
    QPushButton* copyMessageButton = new QPushButton("📝 复制消息");
    copyMessageButton->setStyleSheet("QPushButton { background-color: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #1e7e34; }");
    connect(copyMessageButton, &QPushButton::clicked, [&entry]() {
        QApplication::clipboard()->setText(entry.message());
    });
    buttonLayout->addWidget(copyMessageButton);

    buttonLayout->addStretch();

    // 关闭按钮
    QPushButton* closeButton = new QPushButton("✖️ 关闭");
    closeButton->setStyleSheet("QPushButton { background-color: #6c757d; color: white; padding: 8px 16px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #545b62; }");
    connect(closeButton, &QPushButton::clicked, &dialog, &QDialog::accept);
    buttonLayout->addWidget(closeButton);

    mainLayout->addLayout(buttonLayout);

    // 显示对话框
    dialog.exec();

    isDialogOpen = false;
}
