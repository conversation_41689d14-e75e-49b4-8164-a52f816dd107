#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QProgressBar>
#include <QTextEdit>
#include <QFileDialog>
#include <QMessageBox>
#include <QDebug>
#include <QTimer>

#include "asyncfilereader.h"

/**
 * @brief 测试重构后的AsyncFileReader的简单应用
 */
class AsyncFileReaderTestWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit AsyncFileReaderTestWindow(QWidget *parent = nullptr)
        : QMainWindow(parent)
        , m_reader(nullptr)
        , m_progressBar(nullptr)
        , m_statusLabel(nullptr)
        , m_logOutput(nullptr)
        , m_startButton(nullptr)
        , m_cancelButton(nullptr)
    {
        setupUI();
        setupAsyncReader();
    }

    ~AsyncFileReaderTestWindow()
    {
        if (m_reader) {
            m_reader->cancelReading();
        }
    }

private slots:
    void onStartButtonClicked()
    {
        QString fileName = QFileDialog::getOpenFileName(this, 
            "选择日志文件", "", "日志文件 (*.log *.txt);;所有文件 (*.*)");
        
        if (!fileName.isEmpty()) {
            m_logOutput->clear();
            m_progressBar->setValue(0);
            m_statusLabel->setText("开始读取文件...");
            
            // 开始异步读取
            m_reader->startReading(fileName, "UTF-8", 1000); // 最多读取1000条
            
            m_startButton->setEnabled(false);
            m_cancelButton->setEnabled(true);
        }
    }

    void onCancelButtonClicked()
    {
        if (m_reader) {
            m_reader->cancelReading();
        }
    }

    void onReadingStarted(const QString& filePath, int estimatedLines)
    {
        m_statusLabel->setText(QString("开始读取: %1 (估计 %2 行)")
                              .arg(QFileInfo(filePath).fileName())
                              .arg(estimatedLines));
        m_progressBar->setMaximum(estimatedLines);
        
        qDebug() << "Reading started:" << filePath << "estimated lines:" << estimatedLines;
    }

    void onProgressUpdated(int processedLines, int totalLines, int percentage)
    {
        m_progressBar->setValue(processedLines);
        m_statusLabel->setText(QString("处理中: %1/%2 行 (%3%)")
                              .arg(processedLines)
                              .arg(totalLines)
                              .arg(percentage));
        
        qDebug() << "Progress:" << processedLines << "/" << totalLines << "(" << percentage << "%)";
    }

    void onDataChunkReady(const QVector<LogEntry>& entries, bool isLastChunk)
    {
        // 显示前几条日志条目
        for (int i = 0; i < qMin(5, entries.size()); ++i) {
            const LogEntry& entry = entries[i];
            QString logText = QString("[%1] %2: %3")
                             .arg(entry.getTimestamp().toString("hh:mm:ss"))
                             .arg(entry.getLevelString())
                             .arg(entry.getMessage());
            m_logOutput->append(logText);
        }
        
        if (entries.size() > 5) {
            m_logOutput->append(QString("... 还有 %1 条日志条目").arg(entries.size() - 5));
        }
        
        qDebug() << "Data chunk ready:" << entries.size() << "entries, last chunk:" << isLastChunk;
    }

    void onReadingCompleted(int totalEntries, qint64 elapsedMs)
    {
        m_statusLabel->setText(QString("读取完成: %1 条日志，耗时 %2 毫秒")
                              .arg(totalEntries)
                              .arg(elapsedMs));
        
        m_startButton->setEnabled(true);
        m_cancelButton->setEnabled(false);
        
        QMessageBox::information(this, "完成", 
                               QString("成功读取 %1 条日志条目\n耗时: %2 毫秒")
                               .arg(totalEntries).arg(elapsedMs));
        
        qDebug() << "Reading completed:" << totalEntries << "entries in" << elapsedMs << "ms";
    }

    void onReadingCancelled()
    {
        m_statusLabel->setText("读取已取消");
        m_startButton->setEnabled(true);
        m_cancelButton->setEnabled(false);
        
        qDebug() << "Reading cancelled";
    }

    void onErrorOccurred(const QString& error)
    {
        m_statusLabel->setText("错误: " + error);
        m_startButton->setEnabled(true);
        m_cancelButton->setEnabled(false);
        
        QMessageBox::critical(this, "错误", error);
        
        qDebug() << "Error occurred:" << error;
    }

private:
    void setupUI()
    {
        setWindowTitle("AsyncFileReader 重构测试");
        setMinimumSize(600, 400);

        QWidget* centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);

        QVBoxLayout* mainLayout = new QVBoxLayout(centralWidget);

        // 按钮布局
        QHBoxLayout* buttonLayout = new QHBoxLayout();
        m_startButton = new QPushButton("选择文件并开始读取", this);
        m_cancelButton = new QPushButton("取消", this);
        m_cancelButton->setEnabled(false);
        
        buttonLayout->addWidget(m_startButton);
        buttonLayout->addWidget(m_cancelButton);
        buttonLayout->addStretch();

        // 状态和进度
        m_statusLabel = new QLabel("准备就绪", this);
        m_progressBar = new QProgressBar(this);

        // 日志输出
        m_logOutput = new QTextEdit(this);
        m_logOutput->setReadOnly(true);

        mainLayout->addLayout(buttonLayout);
        mainLayout->addWidget(m_statusLabel);
        mainLayout->addWidget(m_progressBar);
        mainLayout->addWidget(m_logOutput);

        // 连接信号
        connect(m_startButton, &QPushButton::clicked, this, &AsyncFileReaderTestWindow::onStartButtonClicked);
        connect(m_cancelButton, &QPushButton::clicked, this, &AsyncFileReaderTestWindow::onCancelButtonClicked);
    }

    void setupAsyncReader()
    {
        m_reader = new AsyncFileReader(this);
        
        // 连接所有信号
        connect(m_reader, &AsyncFileReader::readingStarted,
                this, &AsyncFileReaderTestWindow::onReadingStarted);
        connect(m_reader, &AsyncFileReader::progressUpdated,
                this, &AsyncFileReaderTestWindow::onProgressUpdated);
        connect(m_reader, &AsyncFileReader::dataChunkReady,
                this, &AsyncFileReaderTestWindow::onDataChunkReady);
        connect(m_reader, &AsyncFileReader::readingCompleted,
                this, &AsyncFileReaderTestWindow::onReadingCompleted);
        connect(m_reader, &AsyncFileReader::readingCancelled,
                this, &AsyncFileReaderTestWindow::onReadingCancelled);
        connect(m_reader, &AsyncFileReader::errorOccurred,
                this, &AsyncFileReaderTestWindow::onErrorOccurred);
    }

private:
    AsyncFileReader* m_reader;
    QProgressBar* m_progressBar;
    QLabel* m_statusLabel;
    QTextEdit* m_logOutput;
    QPushButton* m_startButton;
    QPushButton* m_cancelButton;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    qDebug() << "Starting AsyncFileReader refactor test...";

    AsyncFileReaderTestWindow window;
    window.show();

    return app.exec();
}

#include "test_asyncfilereader_refactor.moc"
