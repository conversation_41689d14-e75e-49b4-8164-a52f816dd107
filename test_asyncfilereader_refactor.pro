QT += core widgets

CONFIG += c++11

TARGET = test_asyncfilereader_refactor
TEMPLATE = app

HEADERS += \
    logentry.h \
    logviewer_global.h \
    asyncfilereader.h \
    filereaderworker.h

SOURCES += \
    test_asyncfilereader_refactor.cpp \
    logentry.cpp \
    asyncfilereader.cpp \
    filereaderworker.cpp

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target
