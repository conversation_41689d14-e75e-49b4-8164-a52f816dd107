#include "asyncfilereader.h"
#include "filereaderworker.h"
#include <QDebug>

AsyncFileReader::AsyncFileReader(QObject* parent)
    : QObject(parent)
    , m_workerThread(nullptr)
    , m_worker(nullptr)
{
    // 创建工作线程
    m_workerThread = new QThread(this);

    // 创建工作对象
    m_worker = new FileReaderWorker();

    // 将工作对象移动到工作线程 - Qt标准做法
    m_worker->moveToThread(m_workerThread);

    // 连接内部信号到工作对象
    connect(this, &AsyncFileReader::startReadingRequested,
            m_worker, &FileReaderWorker::startReading);
    connect(this, &AsyncFileReader::cancelReadingRequested,
            m_worker, &FileReaderWorker::cancelReading);
    connect(this, &AsyncFileReader::pauseReadingRequested,
            m_worker, &FileReaderWorker::pauseReading);
    connect(this, &AsyncFileReader::resumeReadingRequested,
            m_worker, &FileReaderWorker::resumeReading);

    // 转发工作对象的信号
    connect(m_worker, &FileReaderWorker::readingStarted,
            this, &AsyncFileReader::readingStarted);
    connect(m_worker, &FileReaderWorker::progressUpdated,
            this, &AsyncFileReader::progressUpdated);
    connect(m_worker, &FileReaderWorker::dataChunkReady,
            this, &AsyncFileReader::dataChunkReady);
    connect(m_worker, &FileReaderWorker::readingCompleted,
            this, &AsyncFileReader::readingCompleted);
    connect(m_worker, &FileReaderWorker::readingCancelled,
            this, &AsyncFileReader::readingCancelled);
    connect(m_worker, &FileReaderWorker::errorOccurred,
            this, &AsyncFileReader::errorOccurred);

    // 启动工作线程
    m_workerThread->start();

    qDebug() << "AsyncFileReader created with worker thread";
}

AsyncFileReader::~AsyncFileReader()
{
    // 确保线程正确停止
    if (m_workerThread && m_workerThread->isRunning()) {
        // 停止工作线程
        m_workerThread->quit();
        m_workerThread->wait(3000); // 等待最多3秒
    }

    // 删除工作对象
    if (m_worker) {
        m_worker->deleteLater();
        m_worker = nullptr;
    }

    qDebug() << "AsyncFileReader destroyed";
}

void AsyncFileReader::startReading(const QString& filePath, const QString& encoding, int maxEntries)
{
    qDebug() << "AsyncFileReader::startReading called for:" << filePath;

    // 发送信号给工作对象
    emit startReadingRequested(filePath, encoding, maxEntries);
}

void AsyncFileReader::cancelReading()
{
    qDebug() << "AsyncFileReader::cancelReading called";
    emit cancelReadingRequested();
}

void AsyncFileReader::pauseReading()
{
    qDebug() << "AsyncFileReader::pauseReading called";
    emit pauseReadingRequested();
}

void AsyncFileReader::resumeReading()
{
    qDebug() << "AsyncFileReader::resumeReading called";
    emit resumeReadingRequested();
}

AsyncFileReader::ReadStatus AsyncFileReader::getStatus() const
{
    if (m_worker) {
        return static_cast<ReadStatus>(m_worker->getStatus());
    }
    return Idle;
}

bool AsyncFileReader::isReading() const
{
    if (m_worker) {
        return m_worker->isReading();
    }
    return false;
}

void AsyncFileReader::setChunkSize(int chunkSize)
{
    if (m_worker) {
        m_worker->setChunkSize(chunkSize);
    }
}

void AsyncFileReader::setProgressUpdateInterval(int intervalMs)
{
    if (m_worker) {
        m_worker->setProgressUpdateInterval(intervalMs);
    }
}


