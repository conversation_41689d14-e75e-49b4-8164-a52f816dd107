#ifndef ASYNCFILEREADER_H
#define ASYNCFILEREADER_H

#include "logentry.h"
#include "logviewer_global.h"
#include <QObject>
#include <QThread>

// 前向声明
class FileReaderWorker;

/**
 * @brief 异步文件读取器接口类
 *
 * 管理工作线程和FileReaderWorker，提供异步文件读取功能
 * 使用Qt标准的moveToThread()模式
 * 特点：
 * - 真正的异步执行，不阻塞UI线程
 * - 简化的线程管理
 * - 清晰的职责分离
 * - 符合Qt最佳实践
 */
class LOGVIEWER_EXPORT AsyncFileReader : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 读取状态枚举
     */
    enum ReadStatus {
        Idle,           ///< 空闲状态
        Reading,        ///< 正在读取
        Paused,         ///< 已暂停
        Cancelled,      ///< 已取消
        Completed,      ///< 已完成
        Error           ///< 错误状态
    };

    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit AsyncFileReader(QObject* parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~AsyncFileReader();

    /**
     * @brief 开始异步读取文件
     * @param filePath 文件路径
     * @param encoding 文件编码
     * @param maxEntries 最大读取条目数（0表示无限制）
     */
    void startReading(const QString& filePath, const QString& encoding = "UTF-8", int maxEntries = 0);
    
    /**
     * @brief 取消读取操作
     */
    void cancelReading();
    
    /**
     * @brief 暂停读取操作
     */
    void pauseReading();
    
    /**
     * @brief 恢复读取操作
     */
    void resumeReading();
    
    /**
     * @brief 获取当前状态
     * @return 读取状态
     */
    ReadStatus getStatus() const;

    /**
     * @brief 检查是否正在读取
     * @return 是否正在读取
     */
    bool isReading() const;

    /**
     * @brief 设置分块大小
     * @param chunkSize 每次读取的行数
     */
    void setChunkSize(int chunkSize);

    /**
     * @brief 设置进度更新间隔
     * @param intervalMs 间隔毫秒数
     */
    void setProgressUpdateInterval(int intervalMs);

signals:
    // 内部信号，用于与Worker通信
    void startReadingRequested(const QString& filePath, const QString& encoding, int maxEntries);
    void cancelReadingRequested();
    void pauseReadingRequested();
    void resumeReadingRequested();

public slots:
    // 这些槽函数转发给Worker

signals:
    /**
     * @brief 读取开始信号
     * @param filePath 文件路径
     * @param estimatedLines 估计行数
     */
    void readingStarted(const QString& filePath, int estimatedLines);

    /**
     * @brief 进度更新信号
     * @param processedLines 已处理行数
     * @param totalLines 总行数（估计）
     * @param percentage 完成百分比
     */
    void progressUpdated(int processedLines, int totalLines, int percentage);

    /**
     * @brief 数据块读取完成信号
     * @param entries 读取到的日志条目
     * @param isLastChunk 是否为最后一块
     */
    void dataChunkReady(const QVector<LogEntry>& entries, bool isLastChunk);

    /**
     * @brief 读取完成信号
     * @param totalEntries 总条目数
     * @param elapsedMs 耗时毫秒数
     */
    void readingCompleted(int totalEntries, qint64 elapsedMs);

    /**
     * @brief 读取取消信号
     */
    void readingCancelled();

    /**
     * @brief 错误信号
     * @param error 错误描述
     */
    void errorOccurred(const QString& error);

private:
    // 线程管理
    QThread* m_workerThread;           ///< 工作线程
    FileReaderWorker* m_worker;        ///< 工作对象
};

#endif // ASYNCFILEREADER_H
